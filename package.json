{"name": "mystique", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@marsidev/react-turnstile": "^1.1.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.0", "drizzle-orm": "^0.44.3", "import": "^0.0.6", "lucide-react": "^0.525.0", "next": "15.4.1", "pg": "^8.16.3", "prettier-plugin-organize-imports": "^4.1.0", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "prettier": "3.6.2", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}}