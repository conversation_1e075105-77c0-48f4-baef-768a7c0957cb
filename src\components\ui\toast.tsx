"use client";

import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { CheckIcon, TriangleAlert, X } from "lucide-react";
import { Toaster as Sonner, toast as sonner, ToasterProps } from "sonner";
import { useAppearance } from "../providers/appearance-provider";

export const Toaster = ({ ...props }: ToasterProps) => (
	<Sonner
		theme={useAppearance().theme as ToasterProps["theme"]}
		position="bottom-right"
		duration={3000}
		closeButton
		visibleToasts={3}
		swipeDirections={["left", "right"]}
		className="toaster group"
		style={
			{
				"--normal-bg": "var(--popover)",
				"--normal-text": "var(--popover-foreground)",
				"--normal-border": "var(--border)",
			} as React.CSSProperties
		}
		{...props}
	/>
);

const toastStyles = cva("w-full flex rounded-lg shadow-lg ring-1 gap-3 ring-black/5 w-full md:w-[364px] items-center p-4 font-pixelify", {
	variants: {
		variant: {
			default: "bg-background text-foreground",
			success: "bg-primary text-foreground",
			error: "bg-destructive text-foreground",
			warning: "bg-yellow-500 text-foreground",
		},
	},
	defaultVariants: {
		variant: "default",
	},
});

export const toast = {
	success: (msg: string) => sonner.custom((id) => <Toast title={msg} variant="success" />),
	error: (msg: string) => sonner.custom((id) => <Toast title={msg} variant="error" />),
	warning: (msg: string) => sonner.custom((id) => <Toast title={msg} variant="warning" />),
	default: (msg: string) => sonner.custom((id) => <Toast title={msg} variant="default" />),
};

export const Toast = ({
	title,
	variant,
}: {
	title: string | React.ReactNode;
} & VariantProps<typeof toastStyles>) => {
	const Icon = variant === "success" ? CheckIcon : variant === "error" ? X : variant === "warning" ? TriangleAlert : null;
	return (
		<div className="relative font-pixelify select-none">
			<div className={cn(toastStyles({ variant }))}>
				{Icon && <Icon />}
				<p className={`text-sm font-medium ${variant === "default" ? "text-center w-full" : "text-start"}`}>{title}</p>
			</div>

			<div className="absolute -top-1.5 w-1/2 left-1.5 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -top-1.5 w-1/2 right-1.5 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 left-1.5 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 right-1.5 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-1 -left-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-1 -left-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-1 -right-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-1 -right-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
		</div>
	);
};
