{"printWidth": 140, "tabWidth": 2, "useTabs": true, "semi": true, "singleQuote": false, "trailingComma": "all", "bracketSpacing": true, "arrowParens": "always", "endOfLine": "lf", "htmlWhitespaceSensitivity": "strict", "bracketSameLine": true, "singleAttributePerLine": false, "jsxSingleQuote": false, "quoteProps": "consistent", "plugins": ["prettier-plugin-organize-imports"], "objectWrap": "preserve", "overrides": [{"files": "*.json", "options": {"trailingComma": "none"}}, {"files": "*.lock", "options": {"trailingComma": "none"}}]}