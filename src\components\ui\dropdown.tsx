"use client";

import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu";
import { cva, type VariantProps } from "class-variance-authority";
import { CheckIcon, ChevronRightIcon, CircleIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

export const dropDownVariants = cva("", {
	variants: {
		font: {
			normal: "",
			retro: "retro",
		},
	},
	defaultVariants: {
		font: "retro",
	},
});

export type DropdownMenuContentProps = React.ComponentProps<typeof DropdownMenuPrimitive.Content> & VariantProps<typeof dropDownVariants>;

export const DropdownMenu = DropdownMenuPrimitive.Root;
export const DropdownMenuPortal = DropdownMenuPrimitive.Portal;
export const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;
export const DropdownMenuGroup = DropdownMenuPrimitive.Group;
export const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;
export const DropdownMenuSub = DropdownMenuPrimitive.Sub;

export const DropdownMenuContent = ({ children, className, font, ...props }: DropdownMenuContentProps) => (
	<DropdownMenuPrimitive.Portal>
		<DropdownMenuPrimitive.Content
			data-slot="dropdown-menu-content"
			sideOffset={4}
			className={cn("mt-1 py-2", font !== "normal" && "retro", className)}
			{...props}>
			{children}

			<div className="absolute inset-0 -mx-1.5 border-x-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
			<div className="absolute inset-0 -my-1.5 border-y-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
		</DropdownMenuPrimitive.Content>
	</DropdownMenuPrimitive.Portal>
);

export const DropdownMenuSubContent = ({ children, className, font, ...props }: DropdownMenuContentProps) => (
	<DropdownMenuPrimitive.SubContent
		data-slot="dropdown-menu-sub-content"
		className={cn("bg-card", font !== "normal" && "retro", className)}
		{...props}>
		{children}

		<div className="absolute inset-0 -mx-1.5 border-x-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
		<div className="absolute inset-0 -my-1.5 border-y-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
	</DropdownMenuPrimitive.SubContent>
);

export const DropdownMenuItem = ({ className, children, ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Item>) => (
	<DropdownMenuPrimitive.Item
		data-slot="dropdown-menu-item"
		className={cn(
			"hover:bg-transparent active:bg-transparent focus:bg-transparent rounded-none border-dashed border-y-3 border-transparent focus:border-foreground hover:border-foreground dark:focus:border-ring bg-transparent",
			className,
		)}
		{...props}>
		{children}
	</DropdownMenuPrimitive.Item>
);

export const DropdownMenuSubTrigger = ({
	className,
	children,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger>) => (
	<DropdownMenuPrimitive.SubTrigger
		data-slot="dropdown-menu-sub-trigger"
		className={cn(
			"hover:bg-transparent active:bg-transparent focus:bg-transparent rounded-none border-dashed border-y-4 border-transparent focus:border-foreground hover:border-foreground dark:focus:border-ring bg-transparent data-[state=open]:bg-transparent data-[state=open]:border-foreground dark:data-[state=open]:border-ring",
			className,
		)}
		{...props}>
		{children}
		<ChevronRightIcon className="ml-auto size-4" />
	</DropdownMenuPrimitive.SubTrigger>
);

export const DropdownMenuLabel = ({
	className,
	inset,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & { inset?: boolean }) => (
	<DropdownMenuPrimitive.Label
		data-slot="dropdown-menu-label"
		data-inset={inset}
		className={cn("px-2 py-1.5 text-sm font-medium", inset && "pl-8", className)}
		{...props}
	/>
);

export const DropdownMenuSeparator = ({ className, ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) => (
	<DropdownMenuPrimitive.Separator data-slot="dropdown-menu-separator" className={cn("bg-border -mx-1 my-1 h-px", className)} {...props} />
);

export const DropdownMenuShortcut = ({ className, ...props }: React.ComponentProps<"span">) => (
	<span data-slot="dropdown-menu-shortcut" className={cn("text-muted-foreground ml-auto text-xs tracking-widest", className)} {...props} />
);

export const DropdownMenuCheckboxItem = ({
	className,
	children,
	checked,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) => (
	<DropdownMenuPrimitive.CheckboxItem
		data-slot="dropdown-menu-checkbox-item"
		className={cn(
			"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm select-none outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			className,
		)}
		checked={checked}
		{...props}>
		<span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
			<DropdownMenuPrimitive.ItemIndicator>
				<CheckIcon className="size-4" />
			</DropdownMenuPrimitive.ItemIndicator>
		</span>
		{children}
	</DropdownMenuPrimitive.CheckboxItem>
);

export const DropdownMenuRadioItem = ({ className, children, ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) => (
	<DropdownMenuPrimitive.RadioItem
		data-slot="dropdown-menu-radio-item"
		className={cn(
			"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm select-none outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			className,
		)}
		{...props}>
		<span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
			<DropdownMenuPrimitive.ItemIndicator>
				<CircleIcon className="size-2 fill-current" />
			</DropdownMenuPrimitive.ItemIndicator>
		</span>
		{children}
	</DropdownMenuPrimitive.RadioItem>
);
