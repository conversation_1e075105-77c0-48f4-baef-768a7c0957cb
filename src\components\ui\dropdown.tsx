"use client";

import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu";
import { cva, type VariantProps } from "class-variance-authority";
import { CheckIcon, ChevronRightIcon, CircleIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

export const dropDownVariants = cva("", {
	variants: {
		font: {
			normal: "",
			retro: "font-pixelify",
		},
	},
	defaultVariants: {
		font: "retro",
	},
});

export type DropdownMenuContentProps = React.ComponentProps<typeof DropdownMenuPrimitive.Content> & VariantProps<typeof dropDownVariants>;

export const DropdownMenu = DropdownMenuPrimitive.Root;
export const DropdownMenuPortal = DropdownMenuPrimitive.Portal;
export const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;
export const DropdownMenuGroup = DropdownMenuPrimitive.Group;
export const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;
export const DropdownMenuSub = DropdownMenuPrimitive.Sub;

export const DropdownMenuContent = ({ children, className, font, ...props }: DropdownMenuContentProps) => (
	<DropdownMenuPrimitive.Portal>
		<DropdownMenuPrimitive.Content
			data-slot="dropdown-menu-content"
			sideOffset={4}
			className={cn(
				"z-50 min-w-[8rem] overflow-hidden rounded-none bg-popover p-1 text-popover-foreground shadow-md border-2 border-foreground dark:border-ring",
				dropDownVariants({ font }),
				className,
			)}
			{...props}>
			{children}

			<div className="absolute inset-0 -mx-1.5 border-x-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
			<div className="absolute inset-0 -my-1.5 border-y-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
		</DropdownMenuPrimitive.Content>
	</DropdownMenuPrimitive.Portal>
);

export const DropdownMenuSubContent = ({ children, className, font, ...props }: DropdownMenuContentProps) => (
	<DropdownMenuPrimitive.SubContent
		data-slot="dropdown-menu-sub-content"
		className={cn(
			"z-50 min-w-[8rem] overflow-hidden rounded-none bg-popover p-1 text-popover-foreground shadow-md border-2 border-foreground dark:border-ring",
			dropDownVariants({ font }),
			className,
		)}
		{...props}>
		{children}

		<div className="absolute inset-0 -mx-1.5 border-x-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
		<div className="absolute inset-0 -my-1.5 border-y-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
	</DropdownMenuPrimitive.SubContent>
);

export const DropdownMenuItem = ({ className, children, ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Item>) => (
	<DropdownMenuPrimitive.Item
		data-slot="dropdown-menu-item"
		className={cn(
			"relative flex cursor-default select-none items-center rounded-none px-2 py-1.5 text-sm outline-none transition-colors",
			"hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
			"data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			"border-2 border-transparent hover:border-foreground focus:border-foreground dark:hover:border-ring dark:focus:border-ring",
			className,
		)}
		{...props}>
		{children}
	</DropdownMenuPrimitive.Item>
);

export const DropdownMenuSubTrigger = ({
	className,
	children,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger>) => (
	<DropdownMenuPrimitive.SubTrigger
		data-slot="dropdown-menu-sub-trigger"
		className={cn(
			"relative flex cursor-default select-none items-center rounded-none px-2 py-1.5 text-sm outline-none transition-colors",
			"hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
			"data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			"border-2 border-transparent hover:border-foreground focus:border-foreground dark:hover:border-ring dark:focus:border-ring",
			"data-[state=open]:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:border-foreground dark:data-[state=open]:border-ring",
			className,
		)}
		{...props}>
		{children}
		<ChevronRightIcon className="ml-auto size-4" />
	</DropdownMenuPrimitive.SubTrigger>
);

export const DropdownMenuLabel = ({
	className,
	inset,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & { inset?: boolean }) => (
	<DropdownMenuPrimitive.Label
		data-slot="dropdown-menu-label"
		data-inset={inset}
		className={cn("px-2 py-1.5 text-sm font-semibold text-foreground", inset && "pl-8", className)}
		{...props}
	/>
);

export const DropdownMenuSeparator = ({ className, ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) => (
	<DropdownMenuPrimitive.Separator data-slot="dropdown-menu-separator" className={cn("-mx-1 my-1 h-px bg-muted", className)} {...props} />
);

export const DropdownMenuShortcut = ({ className, ...props }: React.ComponentProps<"span">) => (
	<span data-slot="dropdown-menu-shortcut" className={cn("text-muted-foreground ml-auto text-xs tracking-widest", className)} {...props} />
);

export const DropdownMenuCheckboxItem = ({
	className,
	children,
	checked,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) => (
	<DropdownMenuPrimitive.CheckboxItem
		data-slot="dropdown-menu-checkbox-item"
		className={cn(
			"relative flex cursor-default select-none items-center rounded-none py-1.5 pl-8 pr-2 text-sm outline-none transition-colors",
			"hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
			"data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			"border-2 border-transparent hover:border-foreground focus:border-foreground dark:hover:border-ring dark:focus:border-ring",
			className,
		)}
		checked={checked}
		{...props}>
		<span className="absolute left-2 flex size-3.5 items-center justify-center">
			<DropdownMenuPrimitive.ItemIndicator>
				<CheckIcon className="size-4" />
			</DropdownMenuPrimitive.ItemIndicator>
		</span>
		{children}
	</DropdownMenuPrimitive.CheckboxItem>
);

export const DropdownMenuRadioItem = ({ className, children, ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) => (
	<DropdownMenuPrimitive.RadioItem
		data-slot="dropdown-menu-radio-item"
		className={cn(
			"relative flex cursor-default select-none items-center rounded-none py-1.5 pl-8 pr-2 text-sm outline-none transition-colors",
			"hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
			"data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			"border-2 border-transparent hover:border-foreground focus:border-foreground dark:hover:border-ring dark:focus:border-ring",
			className,
		)}
		{...props}>
		<span className="absolute left-2 flex size-3.5 items-center justify-center">
			<DropdownMenuPrimitive.ItemIndicator>
				<CircleIcon className="size-2 fill-current" />
			</DropdownMenuPrimitive.ItemIndicator>
		</span>
		{children}
	</DropdownMenuPrimitive.RadioItem>
);
