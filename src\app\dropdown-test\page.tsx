"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuRadioGroup,
	DropdownMenuRadioItem,
	DropdownMenuSeparator,
	DropdownMenuShortcut,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown";
import { 
	Cloud, 
	CreditCard, 
	Github, 
	Keyboard, 
	LifeBuoy, 
	LogOut, 
	Mail, 
	MessageSquare, 
	Plus, 
	PlusCircle, 
	Settings, 
	User, 
	UserPlus, 
	Users 
} from "lucide-react";
import { useState } from "react";

export default function DropdownTestPage() {
	const [showStatusBar, setShowStatusBar] = useState(true);
	const [showActivityBar, setShowActivityBar] = useState(false);
	const [showPanel, setShowPanel] = useState(false);
	const [position, setPosition] = useState("bottom");

	return (
		<div className="min-h-screen bg-background p-8">
			<div className="max-w-4xl mx-auto space-y-8">
				<div className="text-center space-y-4">
					<h1 className="text-4xl font-bold font-pixelify">8-bit Dropdown Menu Test</h1>
					<p className="text-lg text-muted-foreground">
						Testing the improved dropdown component with 8-bit styling
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
					{/* Basic Dropdown */}
					<div className="space-y-4">
						<h2 className="text-xl font-semibold font-pixelify">Basic Dropdown</h2>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="outline">Open Menu</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="w-56">
								<DropdownMenuLabel>My Account</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuGroup>
									<DropdownMenuItem>
										<User className="mr-2 h-4 w-4" />
										<span>Profile</span>
										<DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
									</DropdownMenuItem>
									<DropdownMenuItem>
										<CreditCard className="mr-2 h-4 w-4" />
										<span>Billing</span>
										<DropdownMenuShortcut>⌘B</DropdownMenuShortcut>
									</DropdownMenuItem>
									<DropdownMenuItem>
										<Settings className="mr-2 h-4 w-4" />
										<span>Settings</span>
										<DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
									</DropdownMenuItem>
									<DropdownMenuItem>
										<Keyboard className="mr-2 h-4 w-4" />
										<span>Keyboard shortcuts</span>
										<DropdownMenuShortcut>⌘K</DropdownMenuShortcut>
									</DropdownMenuItem>
								</DropdownMenuGroup>
								<DropdownMenuSeparator />
								<DropdownMenuItem>
									<LogOut className="mr-2 h-4 w-4" />
									<span>Log out</span>
									<DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					{/* Dropdown with Submenu */}
					<div className="space-y-4">
						<h2 className="text-xl font-semibold font-pixelify">With Submenu</h2>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="outline">Open with Submenu</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="w-56">
								<DropdownMenuLabel>Actions</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuGroup>
									<DropdownMenuItem>
										<User className="mr-2 h-4 w-4" />
										<span>Profile</span>
									</DropdownMenuItem>
									<DropdownMenuSub>
										<DropdownMenuSubTrigger>
											<UserPlus className="mr-2 h-4 w-4" />
											<span>Invite users</span>
										</DropdownMenuSubTrigger>
										<DropdownMenuSubContent>
											<DropdownMenuItem>
												<Mail className="mr-2 h-4 w-4" />
												<span>Email</span>
											</DropdownMenuItem>
											<DropdownMenuItem>
												<MessageSquare className="mr-2 h-4 w-4" />
												<span>Message</span>
											</DropdownMenuItem>
											<DropdownMenuSeparator />
											<DropdownMenuItem>
												<PlusCircle className="mr-2 h-4 w-4" />
												<span>More...</span>
											</DropdownMenuItem>
										</DropdownMenuSubContent>
									</DropdownMenuSub>
									<DropdownMenuItem>
										<Plus className="mr-2 h-4 w-4" />
										<span>New Team</span>
										<DropdownMenuShortcut>⌘+T</DropdownMenuShortcut>
									</DropdownMenuItem>
								</DropdownMenuGroup>
								<DropdownMenuSeparator />
								<DropdownMenuItem>
									<Github className="mr-2 h-4 w-4" />
									<span>GitHub</span>
								</DropdownMenuItem>
								<DropdownMenuItem>
									<LifeBuoy className="mr-2 h-4 w-4" />
									<span>Support</span>
								</DropdownMenuItem>
								<DropdownMenuItem disabled>
									<Cloud className="mr-2 h-4 w-4" />
									<span>API</span>
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					{/* Dropdown with Checkboxes and Radio */}
					<div className="space-y-4">
						<h2 className="text-xl font-semibold font-pixelify">With Checkboxes</h2>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="outline">View Options</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="w-56">
								<DropdownMenuLabel>Appearance</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuCheckboxItem
									checked={showStatusBar}
									onCheckedChange={setShowStatusBar}
								>
									Status Bar
								</DropdownMenuCheckboxItem>
								<DropdownMenuCheckboxItem
									checked={showActivityBar}
									onCheckedChange={setShowActivityBar}
									disabled
								>
									Activity Bar
								</DropdownMenuCheckboxItem>
								<DropdownMenuCheckboxItem
									checked={showPanel}
									onCheckedChange={setShowPanel}
								>
									Panel
								</DropdownMenuCheckboxItem>
								<DropdownMenuSeparator />
								<DropdownMenuLabel>Panel Position</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuRadioGroup value={position} onValueChange={setPosition}>
									<DropdownMenuRadioItem value="top">Top</DropdownMenuRadioItem>
									<DropdownMenuRadioItem value="bottom">Bottom</DropdownMenuRadioItem>
									<DropdownMenuRadioItem value="right">Right</DropdownMenuRadioItem>
								</DropdownMenuRadioGroup>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>

				<div className="text-center space-y-4">
					<h2 className="text-2xl font-semibold font-pixelify">Font Variants</h2>
					<div className="flex gap-4 justify-center">
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="outline">Retro Font (Default)</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent font="retro" className="w-56">
								<DropdownMenuLabel>8-bit Style Menu</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuItem>Retro Item 1</DropdownMenuItem>
								<DropdownMenuItem>Retro Item 2</DropdownMenuItem>
								<DropdownMenuItem>Retro Item 3</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>

						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="outline">Normal Font</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent font="normal" className="w-56">
								<DropdownMenuLabel>Normal Style Menu</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuItem>Normal Item 1</DropdownMenuItem>
								<DropdownMenuItem>Normal Item 2</DropdownMenuItem>
								<DropdownMenuItem>Normal Item 3</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>
			</div>
		</div>
	);
}
