import { getSessionCookie } from "better-auth/cookies";
import { type NextRequest, NextResponse } from "next/server";

export const middleware = async (request: NextRequest) => {
	const session = getSessionCookie(request);
	if (request.nextUrl.pathname === "/auth") {
		if (session != null) return NextResponse.redirect(new URL("/dashboard", request.url));
		return NextResponse.next();
	}
	if (!session) return NextResponse.redirect(new URL("/auth", request.url));
	return NextResponse.next();
};

export const config = {
	matcher: ["/(dashboard|auth)/:path*"],
};
