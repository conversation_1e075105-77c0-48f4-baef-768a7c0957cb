"use client";

import { Turnstile } from "@marsidev/react-turnstile";
import { useState } from "react";
import EmailLogin from "./email-login";
import EmailSignup from "./email-signup";
import Github from "./github";
import Google from "./google";

export default () => {
	const [activeTab, setActiveTab] = useState<"login" | "signup">("login");
	const [captchaToken, setCaptchaToken] = useState<string | null>(null);

	return (
		<div className="w-full max-w-md mx-auto">
			<div className="flex border-b border-gray-200 mb-6">
				<button
					onClick={() => setActiveTab("login")}
					className={`flex-1 py-2 px-4 text-center font-medium ${
						activeTab === "login" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500 hover:text-gray-700"
					}`}>
					Sign In
				</button>
				<button
					onClick={() => setActiveTab("signup")}
					className={`flex-1 py-2 px-4 text-center font-medium ${
						activeTab === "signup" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500 hover:text-gray-700"
					}`}>
					Sign Up
				</button>
			</div>

			<div className="space-y-6">
				<div>
					<h3 className="text-sm font-medium text-gray-700 mb-3">Continue with:</h3>
					<div className="flex gap-3">
						<div className="flex-1">
							<Github />
						</div>
						<div className="flex-1">
							<Google />
						</div>
					</div>
				</div>

				<div className="relative">
					<div className="absolute inset-0 flex items-center">
						<div className="w-full border-t border-gray-300" />
					</div>
					<div className="relative flex justify-center text-sm">
						<span className="px-2 bg-white text-gray-500">Or</span>
					</div>
				</div>

				<div className="flex justify-center">
					<Turnstile
						siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY!}
						onSuccess={setCaptchaToken}
						onError={() => setCaptchaToken(null)}
						onExpire={() => setCaptchaToken(null)}
					/>
				</div>

				<div>{activeTab === "login" ? <EmailLogin captchaToken={captchaToken} /> : <EmailSignup captchaToken={captchaToken} />}</div>
			</div>
		</div>
	);
};
