import { AppearanceProvider } from "@/components/providers/appearance-provider";
import { Toaster } from "@/components/ui/toast";
import type { Metadata } from "next";
import { Pixelify_Sans } from "next/font/google";
import "../styles/globals.css";

const pixelifySans = Pixelify_Sans({
	subsets: ["latin", "cyrillic", "latin-ext"],
	weight: ["400", "500", "700"],
	variable: "--font-pixelify",
});

export const metadata: Metadata = { title: "Create Next App", description: "Generated by create next app" };

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
	return (
		<html lang="en" suppressHydrationWarning data-lt-installed>
			<body className={`${pixelifySans.variable} font-pixelify antialiased`}>
				<AppearanceProvider>
					{children}
					<Toaster />
				</AppearanceProvider>
			</body>
		</html>
	);
}
