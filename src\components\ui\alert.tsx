"use client";

import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

export const alertVariants = cva(
	"relative w-full rounded-lg border p-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-1 has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",
	{
		variants: {
			variant: {
				default: "bg-card text-card-foreground",
				destructive: "text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90",
			},
		},
		defaultVariants: {
			variant: "default",
		},
	},
);

export const Alert = ({
	className,
	variant,
	children,
	...props
}: React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>) => {
	return (
		<div className={cn("relative m-1.5", className)} {...props}>
			<div role="alert" data-slot="alert" className={cn("relative rounded-none border-none bg-background", alertVariants({ variant }))}>
				{children}
			</div>

			<div className="absolute -top-1.5 w-1/2 left-1.5 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -top-1.5 w-1/2 right-1.5 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 left-1.5 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 right-1.5 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-1.5 -left-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-1.5 -left-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-1.5 -right-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-1.5 -right-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
		</div>
	);
};

export const AlertTitle = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
	<div data-slot="alert-title" className={cn("font-semibold text-base text-foreground leading-snug", className)} {...props} />
);

export const AlertDescription = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
	<div
		data-slot="alert-description"
		className={cn("text-muted-foreground grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed", className)}
		{...props}
	/>
);
