@import url("https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap");

.retro {
	font-family:
		"Press Start 2P",
		system-ui,
		-apple-system,
		sans-serif;
	line-height: 1.5;
	letter-spacing: 0.5px;
}

/* 8-bit dropdown menu specific styles */
.retro [data-slot="dropdown-menu-content"] {
	image-rendering: pixelated;
	image-rendering: -moz-crisp-edges;
	image-rendering: crisp-edges;
}

.retro [data-slot="dropdown-menu-item"],
.retro [data-slot="dropdown-menu-sub-trigger"],
.retro [data-slot="dropdown-menu-checkbox-item"],
.retro [data-slot="dropdown-menu-radio-item"] {
	font-size: 12px;
	line-height: 1.2;
	padding: 8px 12px;
	transition: all 0.1s ease-in-out;
}

.retro [data-slot="dropdown-menu-label"] {
	font-size: 10px;
	text-transform: uppercase;
	letter-spacing: 1px;
}

.retro [data-slot="dropdown-menu-separator"] {
	height: 2px;
	background: repeating-linear-gradient(
		90deg,
		currentColor 0px,
		currentColor 2px,
		transparent 2px,
		transparent 4px
	);
	opacity: 0.5;
}

/* Pixel-perfect borders */
.retro [data-slot="dropdown-menu-content"] {
	box-shadow: 
		0 0 0 2px currentColor,
		4px 4px 0 0 rgba(0, 0, 0, 0.25);
}

.retro [data-slot="dropdown-menu-item"]:hover,
.retro [data-slot="dropdown-menu-item"]:focus,
.retro [data-slot="dropdown-menu-sub-trigger"]:hover,
.retro [data-slot="dropdown-menu-sub-trigger"]:focus,
.retro [data-slot="dropdown-menu-checkbox-item"]:hover,
.retro [data-slot="dropdown-menu-checkbox-item"]:focus,
.retro [data-slot="dropdown-menu-radio-item"]:hover,
.retro [data-slot="dropdown-menu-radio-item"]:focus {
	transform: translateY(-1px);
	box-shadow: 
		0 0 0 2px currentColor,
		2px 2px 0 0 rgba(0, 0, 0, 0.25);
}

.retro [data-slot="dropdown-menu-item"]:active,
.retro [data-slot="dropdown-menu-sub-trigger"]:active,
.retro [data-slot="dropdown-menu-checkbox-item"]:active,
.retro [data-slot="dropdown-menu-radio-item"]:active {
	transform: translateY(0px);
	box-shadow: 
		0 0 0 2px currentColor,
		1px 1px 0 0 rgba(0, 0, 0, 0.25);
}
