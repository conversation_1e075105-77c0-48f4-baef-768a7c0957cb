"use client";

import { cn } from "@/lib/utils";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { ChevronDownIcon } from "lucide-react";
import * as React from "react";

export const Accordion = ({ ...props }: React.ComponentProps<typeof AccordionPrimitive.Root>) => (
	<AccordionPrimitive.Root data-slot="accordion" {...props} />
);

export const AccordionItem = ({
	className,
	children,
	...props
}: React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item> & {
	asChild?: boolean;
}) => (
	<AccordionPrimitive.Item
		data-slot="accordion-item"
		className={cn("border-dashed border-b-4 border-foreground dark:border-ring relative", className)}
		{...props}>
		{children}
	</AccordionPrimitive.Item>
);

export const AccordionTrigger = ({ className, children, ...props }: React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>) => (
	<AccordionPrimitive.Header className="flex">
		<AccordionPrimitive.Trigger
			data-slot="accordion-trigger"
			className={cn(
				"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",
				className,
			)}
			{...props}>
			{children}
			<ChevronDownIcon className="text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200" />
		</AccordionPrimitive.Trigger>
	</AccordionPrimitive.Header>
);

export const AccordionContent = ({ className, children, ...props }: React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>) => (
	<div className="relative">
		<AccordionPrimitive.Content
			data-slot="accordion-content"
			className={cn("overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down", className)}
			{...props}>
			<div className="pb-4 pt-0 relative z-10 p-1">{children}</div>
		</AccordionPrimitive.Content>

		{/* Shadow content reserved (empty as per original) */}
		<AccordionPrimitive.Content asChild forceMount />
	</div>
);
