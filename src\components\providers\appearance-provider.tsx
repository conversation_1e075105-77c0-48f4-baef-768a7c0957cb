"use client";

import { createContext, useContext, useEffect, useState } from "react";

export type Appearance = (typeof APPEARANCE)[number];
export type Theme = "light" | "dark" | "system";

export const APPEARANCE = ["forest", "city", "dungeon"] as const;
export const THEME = ["light", "dark", "system"] as const;

const APPEARANCE_COOKIE_NAME = "appearance";
const APPEARANCE_STORAGE_KEY = "appearance";
const THEME_COOKIE_NAME = "theme";
const THEME_STORAGE_KEY = "theme";
const DEFAULT_APPEARANCE: Appearance = "forest";
const DEFAULT_THEME: Theme = "system";

type AppearanceContextType = {
	appearance: Appearance;
	setAppearance: (appearance: Appearance) => void;
	theme: Theme;
	setTheme: (theme: Theme) => void;
	resolvedTheme: "light" | "dark";
};

const AppearanceContext = createContext<AppearanceContextType | undefined>(undefined);

const getSystemTheme = (): "light" | "dark" =>
	typeof window === "undefined" ? "light" : window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";

export const AppearanceProvider = ({ children }: { children: React.ReactNode }) => {
	const [appearance, setAppearance] = useState<Appearance>(
		(() => {
			if (typeof window === "undefined") return DEFAULT_APPEARANCE;
			const stored = localStorage.getItem(APPEARANCE_STORAGE_KEY);
			if (stored === "forest" || stored === "city" || stored === "dungeon") return stored;
			const match = document.cookie.match(new RegExp(`(^| )${APPEARANCE_COOKIE_NAME}=([^;]+)`));
			if (match && (match[2] === "forest" || match[2] === "city" || match[2] === "dungeon")) return match[2] as Appearance;
			return DEFAULT_APPEARANCE;
		})(),
	);

	const [theme, setThemeState] = useState<Theme>(
		(() => {
			if (typeof window === "undefined") return DEFAULT_THEME;
			const stored = localStorage.getItem(THEME_STORAGE_KEY);
			if (stored === "light" || stored === "dark" || stored === "system") return stored;
			const match = document.cookie.match(new RegExp(`(^| )${THEME_COOKIE_NAME}=([^;]+)`));
			if (match && (match[2] === "light" || match[2] === "dark" || match[2] === "system")) return match[2] as Theme;
			return DEFAULT_THEME;
		})(),
	);

	const [resolvedTheme, setResolvedTheme] = useState<"light" | "dark">(
		theme === "system" ? getSystemTheme() : theme === "dark" ? "dark" : "light",
	);

	useEffect(() => {
		const html = document.documentElement;
		html.classList.remove("appearance-forest", "appearance-city", "appearance-dungeon");
		html.classList.add(`appearance-${appearance}`);
		localStorage.setItem(APPEARANCE_STORAGE_KEY, appearance);
		document.cookie = `${APPEARANCE_COOKIE_NAME}=${appearance}; path=/; max-age=31536000`;
	}, [appearance]);

	useEffect(() => {
		const html = document.documentElement;
		const newResolvedTheme = theme === "system" ? getSystemTheme() : theme === "dark" ? "dark" : "light";
		setResolvedTheme(newResolvedTheme);
		if (newResolvedTheme === "dark") html.classList.add("dark");
		else html.classList.remove("dark");
		localStorage.setItem(THEME_STORAGE_KEY, theme);
		document.cookie = `${THEME_COOKIE_NAME}=${theme}; path=/; max-age=31536000`;
	}, [theme]);

	useEffect(() => {
		if (theme !== "system") return;
		const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
		const handleChange = () => {
			const newResolvedTheme = getSystemTheme();
			setResolvedTheme(newResolvedTheme);
			const html = document.documentElement;
			if (newResolvedTheme === "dark") html.classList.add("dark");
			else html.classList.remove("dark");
		};
		mediaQuery.addEventListener("change", handleChange);
		return () => mediaQuery.removeEventListener("change", handleChange);
	}, [theme]);

	return (
		<AppearanceContext.Provider
			value={{
				appearance,
				setAppearance,
				theme,
				setTheme: setThemeState,
				resolvedTheme,
			}}>
			{children}
		</AppearanceContext.Provider>
	);
};

export const useAppearance = () => {
	const context = useContext(AppearanceContext);
	if (!context) throw new Error("useAppearance must be used within AppearanceProvider");
	return context;
};
